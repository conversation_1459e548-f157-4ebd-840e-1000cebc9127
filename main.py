import base64
from zhipuai import ZhipuAI

img_path = "4.jpg"
with open(img_path, 'rb') as img_file:
    img_base = base64.b64encode(img_file.read()).decode('utf-8')

client = ZhipuAI(api_key="291d67e8a4ecaed13031ff3656c034cb.sKJwVp58oNjrPz1n") # 填写您自己的APIKey
response = client.chat.completions.create(
    model="glm-4.1v-thinking-flashx",  # 填写需要调用的模型名称
    messages=[
      {
        "role": "user",
        "content": [
          {
            "type": "image_url",
            "image_url": {
                "url": img_base
            }
          },
          {
            "type": "text",
            'text': '读取图片中条形码下面的编号和下面那个卡片的资产编码，资产编码两行一定要组合起来，只返回这两个地方的内容，写成一个列表的格式，只返回这个列表，列表格式如下[”06021DY0000000180343531“, ”06001EM01012052500055892“]，其他的东西都不要'
          }
        ]
      }
    ]
)
print(response.choices[0].message.content)
