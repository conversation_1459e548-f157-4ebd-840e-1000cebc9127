import os
import re

def sort_files_by_name(directory_path):
    # 获取文件夹内所有文件的列表
    files = os.listdir(directory_path)
    
    # 过滤掉文件夹，只保留文件
    files = [f for f in files if os.path.isfile(os.path.join(directory_path, f))]
    
    # 自定义排序规则：根据文件名中的数字和字母进行排序
    def alphanumeric_key(filename):
        # 将文件名中的数字部分提取出来，并保持顺序
        return [int(text) if text.isdigit() else text.lower() for text in re.split(r'(\d+)', filename)]

    # 按文件名递增排序
    files.sort(key=alphanumeric_key)

    # 打印排序后的文件列表
    for file in files:
        print(file)

# 例子：你可以替换下面的路径为你自己的文件夹路径
directory_path = r"./test"  # 替换为实际路径
sort_files_by_name(directory_path)
