import os
from pathlib import Path
import tkinter as tk
from tkinter import filedialog, messagebox

def get_image_files(folder_path: str):
    """获取文件夹中的图片文件，按文件名排序"""
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
    image_files = []

    try:
        for file_path in Path(folder_path).iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                image_files.append(str(file_path))
    except Exception as e:
        print(f"读取文件夹失败: {str(e)}")
        return []

    # 按文件名排序
    image_files.sort(key=lambda x: Path(x).name)
    
    # 打印排序后的文件列表，确保顺序正确
    print("文件夹中的图片文件排序结果:")
    for i, file_path in enumerate(image_files):
        print(f"  索引 {i}: {Path(file_path).name}")
    
    return image_files

def test_indexing():
    """测试索引建立方式"""
    # 创建简单的GUI来选择文件夹
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 选择文件夹
    folder_path = filedialog.askdirectory(title="选择包含图片的文件夹")
    
    if not folder_path:
        print("未选择文件夹，程序退出")
        return
    
    print(f"选择的文件夹: {folder_path}")
    print("=" * 60)
    
    # 获取图片文件
    image_files = get_image_files(folder_path)
    
    if not image_files:
        print("文件夹中没有找到图片文件")
        return
    
    print("=" * 60)
    print(f"总共找到 {len(image_files)} 个图片文件")
    print("=" * 60)
    
    # 模拟建立索引的过程（就像在main程序中一样）
    print("模拟任务提交过程:")
    results = {}
    
    for i, image_path in enumerate(image_files):
        file_name = Path(image_path).name
        print(f"提交任务 索引{i}: {file_name}")
        
        # 模拟存储结果
        results[i] = {
            'file_name': file_name,
            'result': f"模拟结果_{i}",
            'status': '成功',
            'original_index': i
        }
        print(f"存储结果到索引 {i}: 文件名={file_name}")
    
    print("=" * 60)
    print("模拟导出过程:")
    
    # 模拟导出时的排序
    sorted_indices = sorted(results.keys())
    print(f"导出顺序检查 - 结果索引: {sorted_indices}")
    
    data = []
    for i in sorted_indices:
        result_data = results[i]
        print(f"导出第{i}条数据: 文件名={result_data['file_name']}, 状态={result_data['status']}")
        
        # 模拟数据处理
        data1 = f"数据1_{i}"
        data2 = f"数据2_{i}"
        
        row_data = {
            '数据1': data1,
            '数据2': data2
        }
        print(f"索引{i} 文件{result_data['file_name']} 添加到Excel第{len(data)+1}行: '{data1}' | '{data2}'")
        
        data.append(row_data)
    
    print("=" * 60)
    print("最终Excel数据顺序:")
    for i, row in enumerate(data):
        corresponding_file = results[i]['file_name']
        print(f"Excel第{i+1}行: {row} -> 对应文件: {corresponding_file}")
    
    print("=" * 60)
    print("验证顺序一致性:")
    for i in range(len(image_files)):
        original_file = Path(image_files[i]).name
        excel_file = results[i]['file_name']
        if original_file == excel_file:
            print(f"✓ 索引{i}: 文件夹第{i+1}个文件 '{original_file}' == Excel第{i+1}行对应文件 '{excel_file}'")
        else:
            print(f"✗ 索引{i}: 文件夹第{i+1}个文件 '{original_file}' != Excel第{i+1}行对应文件 '{excel_file}'")

if __name__ == "__main__":
    print("图片索引测试程序")
    print("这个程序会模拟main程序中建立索引的方式")
    print("=" * 60)
    test_indexing()
    
    input("\n按回车键退出...")
